import Link from 'next/link';
import { PageNav } from '@/components/common/PageNav';

export default function CarouselsPage() {
  const carousels = [
    {
      title: "Carousel Base",
      href: "/components/carousels/CarouselBase"
    },
    {
      title: "Carousel Base Progress Bar",
      href: "/components/carousels/CarouselBaseProgressBar"
    },
    {
      title: "Carousel Card Stack",
      href: "/components/carousels/CarouselCardStack"
    },
    {
      title: "Carousel Infinite Loop",
      href: "/components/carousels/CarouselInfiniteLoop"
    },
    {
      title: "Carousel Auto Play",
      href: "/components/carousels/CarouselAutoPlay"
    },
    {
      title: "Carousel Fullscreen",
      href: "/components/carousels/CarouselFullscreen"
    },
    {
      title: "Carousel Drag Scroll",
      href: "/components/carousels/CarouselDragScroll"
    },
    {
      title: "Carousel Auto Scroll",
      href: "/components/carousels/CarouselAutoScroll"
    }
  ];

  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {carousels.map((carousel) => (
            <Link key={carousel.href} href={carousel.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{carousel.title}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}