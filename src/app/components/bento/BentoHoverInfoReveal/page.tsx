'use client'

import BentoHoverInfoReveal from '@/components/bento/BentoHoverInfoReveal/BentoHoverInfoReveal'
import { PageNav } from '@/components/common/PageNav'
import ReactLenis from 'lenis/react'

const BentoHoverInfoRevealItems = [
    {
        id: '01',
        title: 'Architecture',
        description: 'Building scalable and maintainable application structures with modern design patterns and best practices.',
        imageSrc: '/images/placeholder1.avif'
    },
    {
        id: '02',
        title: 'Development',
        description: 'Creating robust features with clean code, comprehensive testing, and continuous integration workflows.',
        imageSrc: '/images/placeholder2.avif'
    },
    {
        id: '03',
        title: 'Design',
        description: 'Crafting beautiful user interfaces with intuitive interactions and accessible experiences for all users.',
        imageSrc: '/images/placeholder3.avif'
    },
    {
        id: '04',
        title: 'Performance',
        description: 'Optimizing applications for speed and efficiency with code splitting, lazy loading, and caching strategies.',
        imageSrc: '/images/placeholder4.avif'
    },
    {
        id: '05',
        title: 'Innovation',
        description: 'Exploring cutting-edge technologies and methodologies to deliver next-generation digital experiences.',
        imageSrc: '/images/placeholderwide1.jpg'
    },
]

export default function BentoHoverInfoRevealPage() {
    return (
        <ReactLenis root>
            <PageNav />
            <div className="h-screen flex items-center justify-center">
                <p className="text-base text-center">Scroll down to see the effect</p>
            </div>
            <BentoHoverInfoReveal items={BentoHoverInfoRevealItems} />
            <div className="h-screen flex items-center justify-center">
                <p className="text-base text-center">Scroll down to see the no-images version</p>
            </div>
            <BentoHoverInfoReveal items={BentoHoverInfoRevealItems} showImages={false} />
            <div className="h-screen flex items-center justify-center">
                <p className="text-base text-center">The end</p>
            </div>
        </ReactLenis>
    )
}