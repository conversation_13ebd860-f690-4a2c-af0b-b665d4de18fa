'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import BentoContentReveal, { type BentoContentRevealItem } from '@/components/bento/BentoContentReveal/BentoContentReveal'
import { PageNav } from '@/components/common/PageNav'
import { Code2, Palette, Database, Globe, Shield } from 'lucide-react'

const revealItems: BentoContentRevealItem[] = [
  { icon: Code2, text: 'Development' },
  { icon: Palette, text: 'Design' },
  { icon: Database, text: 'Database' },
  { icon: Globe, text: 'Deployment' },
  { icon: Shield, text: 'Security' }
]

export default function BentoContentRevealPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>
      <BentoContentReveal items={revealItems} />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">The end</p>
      </div>
    </ReactLenis>
  )
}