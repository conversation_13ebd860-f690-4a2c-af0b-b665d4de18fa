'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import BentoDepthThreeD from '@/components/bento/BentoDepthThreeD/BentoDepthThreeD'
import { PageNav } from '@/components/common/PageNav'

const threeDItems = [
  {
    position: 'left' as const,
    image: '/images/placeholdersmall1.avif',
    titleEN: 'Design Architecture',
    descriptionEN: 'Building scalable and maintainable application structures with modern design patterns and best practices.'
  },
  {
    position: 'center' as const,
    image: '/images/placeholdersmall3.avif',
    titleEN: 'Software Development',
    descriptionEN: 'Creating robust features with clean code, comprehensive testing, and continuous integration workflows.',
    isCenter: true
  },
  {
    position: 'right' as const,
    image: '/images/placeholdersmall2.avif',
    titleEN: 'Performance Analysis',
    descriptionEN: 'Optimizing applications for speed and efficiency with code splitting, lazy loading, and caching strategies.'
  }
]

export default function BentoDepthThreeDPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>
      <BentoDepthThreeD items={threeDItems} />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">The end</p>
      </div>
    </ReactLenis>
  )
}