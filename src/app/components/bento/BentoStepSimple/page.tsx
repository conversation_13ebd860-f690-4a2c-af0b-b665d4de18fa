'use client'

import React from 'react'
import { React<PERSON>enis } from 'lenis/react'
import BentoStepSimple from '@/components/bento/BentoStepSimple'
import { PageNav } from '@/components/common/PageNav'

const stepsItems = [
  {
    title: 'Design',
    description: 'With a solid track record in designing websites, I deliver strong and user-friendly digital designs that captivate audiences and drive results for businesses. Creating visual experiences that resonate with users while maintaining brand consistency and pushing creative boundaries forward.'
  },
  {
    title: 'Development',
    description: 'I build scalable websites from scratch that fit seamlessly with design. My focus is on micro animations, transitions and interaction for engaging user experiences. Implementing cutting-edge technologies and best practices to ensure optimal performance, accessibility.'
  },
  {
    title: 'The full package',
    description: 'A complete website from concept to implementation, combining great design sense with development skills to create outstanding projects that exceed expectations. Delivering end-to-end solutions that merge aesthetic excellence with technical precision for truly impactful digital.'
  }
]

export default function BentoStepSimplePage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>
      <section className="min-h-screen flex items-center px-[var(--width-10)]">
        <BentoStepSimple items={stepsItems} />
      </section>
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">End</p>
      </div>
    </ReactLenis>
  )
}