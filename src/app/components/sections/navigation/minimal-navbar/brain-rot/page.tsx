"use client";

import React, { Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";
import NavbarStyleMinimal from "@/components/sections/layouts/navigation/MinimalNavbar";

function BrainRotNavbarContent() {
  const searchParams = useSearchParams();
  const colorTemplate =
    (searchParams.get("theme") as unknown as ColorTemplate) || 1;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "brainRot",
          colorTemplate: Number(colorTemplate) as ColorTemplate,
          textAnimation: "slide",
        }}
      >
        <NavbarStyleMinimal
          buttonText="Menu"
          onButtonClick={() => console.log("Menu clicked")}
          buttonType="slide"
        />
        <div className="h-screen flex items-center justify-center bg-black">
          <h1 className="text-3xl text-white font-black uppercase">
            Brain Rot Minimal Navbar - Theme {colorTemplate}
          </h1>
        </div>
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function BrainRotPage() {
  return (
    <Suspense>
      <BrainRotNavbarContent />
    </Suspense>
  );
}
