"use client";

import React, { Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";
import NavbarStyleMinimal from "@/components/sections/layouts/navigation/MinimalNavbar";

function FuturisticNavbarContent() {
  const searchParams = useSearchParams();
  const colorTemplate =
    (searchParams.get("theme") as unknown as ColorTemplate) || 1;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "futuristicAndOutOfBox",
          colorTemplate: Number(colorTemplate) as ColorTemplate,
          textAnimation: "slide",
        }}
      >
        <NavbarStyleMinimal
          buttonText="Get Started"
          onButtonClick={() => console.log("Get Started clicked")}
          buttonType="slide"
        />
        <div className="h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 to-black">
          <h1 className="text-3xl text-white">
            Futuristic Minimal Navbar - Theme {colorTemplate}
          </h1>
        </div>
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function FuturisticPage() {
  return (
    <Suspense>
      <FuturisticNavbarContent />
    </Suspense>
  );
}
