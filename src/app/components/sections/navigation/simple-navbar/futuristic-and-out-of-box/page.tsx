"use client";

import React, { Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";
import NavbarBase from "@/components/sections/layouts/navigation/NavbarBase";

function FuturisticNavbarContent() {
  const searchParams = useSearchParams();
  const colorTemplate =
    (searchParams.get("theme") as unknown as ColorTemplate) || 1;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "futuristicAndOutOfBox",
          colorTemplate: Number(colorTemplate) as ColorTemplate,
          textAnimation: "slide",
        }}
      >
        <NavbarBase
          leftButtonText="Explore"
          rightButtonText="Connect"
          onLeftButtonClick={() => console.log("Explore clicked")}
          onRightButtonClick={() => console.log("Connect clicked")}
        />
        <div className="h-screen flex items-center justify-center bg-gradient-to-br from-purple-900 to-indigo-900">
          <h1 className="text-3xl text-white">
            Futuristic Simple Navbar - Theme {colorTemplate}
          </h1>
        </div>
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function FuturisticPage() {
  return (
    <Suspense>
      <FuturisticNavbarContent />
    </Suspense>
  );
}
