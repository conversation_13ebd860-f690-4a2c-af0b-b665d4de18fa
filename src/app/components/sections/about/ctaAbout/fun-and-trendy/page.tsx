"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import CtaAbout from "@/components/sections/layouts/about/CtaAbout";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";

function FunAndTrendyAboutContent() {
  const searchParams = useSearchParams();
  const colorTemplate = searchParams.get("theme") || "1";

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "funAndTrendy",
          colorTemplate: Number(colorTemplate) as ColorTemplate,
          textAnimation: "slide",
        }}
      >
        <CtaAbout />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function FunAndTrendyAboutPage() {
  return (
    <Suspense>
      <FunAndTrendyAboutContent />
    </Suspense>
  );
}
