"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import MinimalAbout from "@/components/sections/layouts/about/SplitAbout";
import { useSearchParams } from "next/navigation";
import { SiteThemeProvider, ColorTemplate } from "@/components/sections/ThemeProvider";

function FuturisticAboutPage() {
  const searchParams = useSearchParams();
  const colorTemplate = (searchParams.get("theme") as unknown as ColorTemplate) || 1;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <div className="min-h-svh" />
      <SiteThemeProvider theme={{ styleVariant: 'futuristicAndOutOfBox', colorTemplate: Number(colorTemplate) as ColorTemplate, textAnimation: 'none' }}>
        <MinimalAbout />
      </SiteThemeProvider>
      <div className="min-h-svh" />
    </ReactLenis>
  );
}

export default function FunAndTrendyAboutPage() {
  return (
    <Suspense>
      <FuturisticAboutPage />
    </Suspense>
  );
}
