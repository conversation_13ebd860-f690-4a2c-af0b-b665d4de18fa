"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import Momocoinabout from "@/components/sections/layouts/about/SocialsAbout";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";

function BrainRotAboutPage() {
  const searchParams = useSearchParams();
  const colorTemplate =
    (searchParams.get("theme") as unknown as ColorTemplate) || 1;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "brainRot",
          colorTemplate: Number(colorTemplate) as ColorTemplate,
          textAnimation: "slide",
        }}
      >
        <Momocoinabout
          descriptions={[
            "<PERSON><PERSON> is <PERSON><PERSON>'s cheeky little sister - pastel pink energy ready to pounce born on Solana. Built for fun. By the people. For the people. 100% fair launched, no cabal, no bundles.",
          ]}
        />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function BrainRotSocialsAboutPage() {
  return (
    <Suspense>
      <BrainRotAboutPage />
    </Suspense>
  );
}
