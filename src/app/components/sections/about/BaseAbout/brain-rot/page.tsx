"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import BaseAbout from "@/components/sections/layouts/about/BaseAbout";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";

function BrainRotAboutContent() {
  const searchParams = useSearchParams();
  const colorTemplate = searchParams.get("theme") || "1";

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "brainRot",
          colorTemplate: Number(colorTemplate) as ColorTemplate,
          textAnimation: "slide",
        }}
      >
        <BaseAbout />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function BrainRotBaseAboutPage() {
  return (
    <Suspense>
      <BrainRotAboutContent />
    </Suspense>
  );
}
