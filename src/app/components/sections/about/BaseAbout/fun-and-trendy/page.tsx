'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import BaseAbout from '@/components/sections/layouts/about/BaseAbout'
import { PageNav } from '@/components/common/PageNav'
import { SiteThemeProvider } from '@/components/sections/ThemeProvider'

export default function FunAndTrendyAboutPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider theme={{ styleVariant: 'funAndTrendy', colorTemplate: 1, textAnimation: 'slide' }}>
        <BaseAbout />
      </SiteThemeProvider>
    </ReactLenis>
  )
}