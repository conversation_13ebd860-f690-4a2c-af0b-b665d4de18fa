"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";
import FooterLogoEmphasisBackgroundGradient from "@/components/footer/FooterLogoEmphasisBackgroundGradient";

function FunAndTrendyFooterLogoEmphasisBackgroundGradientContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  return (
    <SiteThemeProvider
      theme={{
        styleVariant: "funAndTrendy",
        colorTemplate: theme,
        textAnimation: "slide",
      }}
    >
      <FooterLogoEmphasisBackgroundGradient />
    </SiteThemeProvider>
  );
}

export default function FunAndTrendyFooterLogoEmphasisBackgroundGradientPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FunAndTrendyFooterLogoEmphasisBackgroundGradientContent />
      </Suspense>
    </ReactLenis>
  );
}
