"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";
import FooterBase from "@/components/footer/FooterBase";

function FunAndTrendyFooterBaseContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  const footerData = {
    columns: [
      {
        title: "Company",
        items: [
          { label: "About", onClick: () => console.log("About clicked") },
          { label: "Blog", onClick: () => console.log("Blog clicked") },
          { label: "Careers", onClick: () => console.log("Careers clicked") },
          { label: "Contact", onClick: () => console.log("Contact clicked") },
        ],
      },
      {
        title: "Resources",
        items: [
          {
            label: "Community",
            onClick: () => console.log("Community clicked"),
          },
          { label: "Support", onClick: () => console.log("Support clicked") },
          { label: "Status", onClick: () => console.log("Status clicked") },
          { label: "Partners", onClick: () => console.log("Partners clicked") },
        ],
      },
      {
        title: "Legal",
        items: [
          { label: "Terms", onClick: () => console.log("Terms clicked") },
          { label: "Privacy", onClick: () => console.log("Privacy clicked") },
          { label: "Cookies", onClick: () => console.log("Cookies clicked") },
          { label: "License", onClick: () => console.log("License clicked") },
        ],
      },
    ],
    copyrightText: "© 2025 | Webild",
    onPrivacyClick: () => console.log("Privacy Policy clicked"),
  };

  return (
    <SiteThemeProvider
      theme={{
        styleVariant: "funAndTrendy",
        colorTemplate: theme,
        textAnimation: "slide",
      }}
    >
      <FooterBase
        columns={footerData.columns}
        copyrightText={footerData.copyrightText}
        onPrivacyClick={footerData.onPrivacyClick}
      />
    </SiteThemeProvider>
  );
}

export default function FunAndTrendyFooterBasePage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FunAndTrendyFooterBaseContent />
      </Suspense>
    </ReactLenis>
  );
}
