"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";
import WalletFooter from "@/components/footer/WalletFooter";

function FuturisticWalletFooterContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  const footerData = {
    logoText: "Webild",
    walletAddress: "******************************************",
    copyButtonText: "Copy",
    copiedText: "Copied!",
    copyrightText: "© 2025 | Webild",
    onSocialClick: (index: number) =>
      console.log(`Social icon ${index + 1} clicked`),
  };

  return (
    <SiteThemeProvider
      theme={{
        styleVariant: "futuristicAndOutOfBox",
        colorTemplate: theme,
        textAnimation: "slide",
      }}
    >
      <WalletFooter
        logoText={footerData.logoText}
        walletAddress={footerData.walletAddress}
        copyButtonText={footerData.copyButtonText}
        copiedText={footerData.copiedText}
        copyrightText={footerData.copyrightText}
        onSocialClick={footerData.onSocialClick}
      />
    </SiteThemeProvider>
  );
}

export default function FuturisticWalletFooterPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <FuturisticWalletFooterContent />
      </Suspense>
    </ReactLenis>
  );
}
