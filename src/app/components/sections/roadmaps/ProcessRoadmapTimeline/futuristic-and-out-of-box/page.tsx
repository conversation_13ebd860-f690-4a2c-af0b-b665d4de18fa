"use client";

import React, { Suspense } from "react";
import { <PERSON>act<PERSON>enis } from "lenis/react";
import ProcessRoadmapTimeline from "@/components/sections/layouts/roadmap/ProcessRoadmap";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";
import type { TimelineProcessFlowItem } from "@/types/timeline";
import {
  Award,
  BarChart2,
  Eye,
  Lightbulb,
  RefreshCw,
  Target,
  Zap,
  Users,
  Rocket,
  Shield,
} from "lucide-react";

function FuturisticTimelineProcessFlowContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) ||
    1) as ColorTemplate;

  const roadmapProcessItems: TimelineProcessFlowItem[] = [
    {
      id: "01",
      title: "Strategic Foundation",
      description:
        "Comprehensive planning and foundation phase including market analysis, competitive research, team building, and strategic roadmap development to ensure project success from the ground up",
      image: "/images/placeholder1.avif",
      items: [
        { icon: Target, text: "Market research & analysis" },
        { icon: Users, text: "Team assembly & roles" },
        { icon: BarChart2, text: "Competitive landscape study" },
      ],
      reverse: false,
    },
    {
      id: "02",
      title: "Design & Innovation",
      description:
        "Creative design and innovation phase focusing on user experience, visual identity, and technical architecture to create compelling and functional solutions",
      image: "/images/placeholder2.avif",
      items: [
        { icon: Eye, text: "User experience design" },
        { icon: Lightbulb, text: "Innovation workshops" },
        { icon: Award, text: "Design system creation" },
      ],
      reverse: true,
    },
    {
      id: "03",
      title: "Development & Testing",
      description:
        "Full-scale development with agile methodologies, continuous integration, comprehensive testing, and quality assurance to deliver robust and scalable solutions",
      image: "/images/placeholder3.avif",
      items: [
        { icon: Zap, text: "Agile development process" },
        { icon: Shield, text: "Security implementation" },
        { icon: RefreshCw, text: "Continuous testing" },
      ],
      reverse: false,
    },
    {
      id: "04",
      title: "Launch & Growth",
      description:
        "Strategic launch execution with marketing campaigns, user onboarding, performance monitoring, and scaling operations for sustainable growth and market expansion",
      image: "/images/placeholder4.avif",
      items: [
        { icon: Rocket, text: "Product launch strategy" },
        { icon: BarChart2, text: "Performance analytics" },
        { icon: Target, text: "Growth optimization" },
      ],
      reverse: true,
    },
  ];

  return (
    <ReactLenis root>
      <div className="min-h-screen">
        <PageNav position="bottom" />
        <SiteThemeProvider
          theme={{
            styleVariant: "futuristicAndOutOfBox",
            colorTemplate: colorTemplate,
            textAnimation: "none",
          }}
        >
          <ProcessRoadmapTimeline items={roadmapProcessItems} />
        </SiteThemeProvider>
      </div>
    </ReactLenis>
  );
}

export default function FuturisticTimelineProcessFlowPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FuturisticTimelineProcessFlowContent />
    </Suspense>
  );
}
