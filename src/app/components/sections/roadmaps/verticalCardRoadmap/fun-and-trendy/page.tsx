"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import VerticalCardRoadmap from "@/components/sections/layouts/roadmap/VerticalCardRoadmap";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";

function FunAndTrendyTimelineContent() {
  const searchParams = useSearchParams();
  const theme = (searchParams.get("theme") as unknown as ColorTemplate) || 1;
  const colorTemplate = Number(theme) as ColorTemplate;

  const roadmapData = [
    {
      title: "Ideation & Planning",
      description:
        "Comprehensive market research, competitive analysis, and strategic planning phase",
      image: "/images/placeholder1.avif",
    },
    {
      title: "Design & Prototyping",
      description:
        "User experience design, wireframing, and interactive prototype development",
      image: "/images/placeholder2.avif",
    },
    {
      title: "MVP Development",
      description:
        "Core feature development and minimum viable product creation",
      image: "/images/placeholder3.avif",
    },
    {
      title: "Testing & Refinement",
      description:
        "Quality assurance, user testing, and iterative improvements",
      image: "/images/placeholder4.avif",
    },
    {
      title: "Launch & Scale",
      description:
        "Product launch, marketing campaigns, and scaling operations",
      image: "/images/placeholder1.avif",
    },
    {
      title: "Growth & Expansion",
      description:
        "Feature expansion, market growth, and strategic partnerships",
      image: "/images/placeholder2.avif",
    },
  ];

  return (
    <ReactLenis root>
      <div className="min-h-screen">
        <PageNav position="bottom" />
        <SiteThemeProvider
          theme={{
            styleVariant: "funAndTrendy",
            colorTemplate,
            textAnimation: "slide",
          }}
        >
          <VerticalCardRoadmap
            items={roadmapData}
            title="Product Development Roadmap"
          />
        </SiteThemeProvider>
      </div>
    </ReactLenis>
  );
}

export default function FunAndTrendyTimelinePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FunAndTrendyTimelineContent />
    </Suspense>
  );
}
