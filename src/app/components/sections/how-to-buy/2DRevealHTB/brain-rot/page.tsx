"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import HowToBuy2D from "@/components/sections/layouts/howtobuy/2DHTB";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";

function BrainRotContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) ||
    1) as ColorTemplate;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "brainRot",
          colorTemplate: colorTemplate,
          textAnimation: "slide",
        }}
      >
        <HowToBuy2D variant="reveal" />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function BrainRotRevealHowToBuyPage() {
  return (
    <Suspense>
      <BrainRotContent />
    </Suspense>
  );
}
