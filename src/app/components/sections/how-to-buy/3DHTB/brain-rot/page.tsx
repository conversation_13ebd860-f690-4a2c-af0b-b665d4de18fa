'use client';

import HowToBuy3D from '@/components/sections/layouts/howtobuy/3DHTB';
import { SiteThemeProvider } from '@/components/sections/ThemeProvider';
import { useSearchParams } from 'next/navigation';
import { ColorTemplate } from '@/components/sections/ThemeProvider';
import { Suspense } from 'react';

function BrainRot3DHTBContent() {
    const searchParams = useSearchParams();
    const themeParam = searchParams.get('theme');
    const colorTemplate: ColorTemplate = themeParam === '2' ? 2 : 1;

    return (
        <SiteThemeProvider theme={{ 
            styleVariant: 'brainRot', 
            colorTemplate: colorTemplate,
            textAnimation: 'slide'
        }}>
            <HowToBuy3D 
                steps={[
                    {
                        position: 'left',
                        image: '/sections/images/brainrotplaceholder.webp',
                        title: 'Step 1: Create Wallet',
                        description: 'Download and set up MetaMask or your preferred crypto wallet to store.'
                    },
                    {
                        position: 'center',
                        image: '/sections/images/brainrotplaceholder.webp',
                        title: 'Step 2: Get ETH',
                        description: 'Purchase Ethereum from an exchange and transfer it to your wallet address.',
                        isCenter: true
                    },
                    {
                        position: 'right',
                        image: '/sections/images/brainrotplaceholder.webp',
                        title: 'Step 3: Swap for $PUDGY',
                        description: 'Connect to Uniswap decentralized exchange and swap your ETH.'
                    }
                ]}
            />
        </SiteThemeProvider>
    );
}

export default function BrainRot3DHTBPage() {
    return (
        <Suspense fallback={<div />}>
            <BrainRot3DHTBContent />
        </Suspense>
    );
}