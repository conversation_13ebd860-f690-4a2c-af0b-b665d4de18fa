'use client';

import React from 'react';
import Link from 'next/link';
import { PageNav } from '@/components/common/PageNav';

const minimaltokenomicsItems: Array<{ name: string; href: string }> = [
  {
    name: 'Fun & Trendy - Theme 1',
    href: '/components/sections/tokenomics/BigNumberTokenomics/fun-and-trendy?theme=1'
  },
  {
    name: 'Fun & Trendy - Theme 2',
    href: '/components/sections/tokenomics/BigNumberTokenomics/fun-and-trendy?theme=2'
  },
  {
    name: 'Futuristic and Out of Box - Theme 1',
    href: '/components/sections/tokenomics/BigNumberTokenomics/futuristic-and-out-of-box?theme=1'
  },
  {
    name: 'Futuristic and Out of Box - Theme 2',
    href: '/components/sections/tokenomics/BigNumberTokenomics/futuristic-and-out-of-box?theme=2'
  }
];

export default function MinimalTokenomicsPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        {minimaltokenomicsItems.length === 0 ? (
          <div className="flex items-center justify-center h-screen">
            <p className="text-lg text-gray-500">No items available yet</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            {minimaltokenomicsItems.map((item) => (
              <Link key={item.name} href={item.href}>
                <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                  <h3 className="text-xl">{item.name}</h3>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </section>
  );
}