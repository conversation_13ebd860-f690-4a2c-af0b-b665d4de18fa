"use client";

import React from "react";
import { ReactLenis } from "lenis/react";
import TextGridTokenomics from "@/components/sections/layouts/tokenomics/TextGridTokenomics";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { Suspense } from "react";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";

function FuturisticTokenomics() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) ||
    1) as ColorTemplate;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "futuristicAndOutOfBox",
          colorTemplate: colorTemplate,
          textAnimation: "none",
        }}
      >
        <TextGridTokenomics />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function FuturisticTokenomicsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FuturisticTokenomics />
    </Suspense>
  );
}
