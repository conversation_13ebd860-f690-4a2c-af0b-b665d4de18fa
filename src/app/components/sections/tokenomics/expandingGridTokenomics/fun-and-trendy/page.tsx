"use client";

import React from "react";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import { Suspense } from "react";
import ExpandingTokenomics from "@/components/sections/layouts/tokenomics/ExpandingGridTokenomics";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";

const cardItems = [
  {
    id: 1,
    title: "1200",
    description:
      "We are committed to acting with honesty and integrity in everything we do. Our decisions are guided by transparency, accountability, and a clear focus on doing what's right.",
  },
  {
    id: 2,
    title: "393",
    description:
      "We believe in giving back to the community and making a positive impact on society through charitable initiatives and social responsibility programs.",
  },
  {
    id: 3,
    title: "700",
    description:
      "We focus on sustainable economic growth and creating value for all stakeholders while maintaining financial responsibility and long-term viability.",
  },
  {
    id: 4,
    title: "1200+",
    description:
      "We prioritize human-centered design and development, ensuring that our solutions serve real human needs and improve quality of life for everyone.",
  },
];

function FunAndTrendyTokenomics() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) ||
    1) as ColorTemplate;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "funAndTrendy",
          colorTemplate: colorTemplate,
          textAnimation: "slide",
        }}
      >
        <ExpandingTokenomics cardItems={cardItems} />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function FunAndTrendyTokenomicsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FunAndTrendyTokenomics />
    </Suspense>
  );
}
