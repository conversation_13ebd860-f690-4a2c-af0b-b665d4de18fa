"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import ImageFAQ from "@/components/sections/layouts/faq/ImageFAQ";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";

function BrainRotImageFAQContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) ||
    1) as ColorTemplate;

  const faqItems = [
    {
      title: "What is this platform about?",
      content:
        "Our platform revolutionizes the way you interact with digital content. We provide cutting-edge tools and services that empower creators and businesses to build amazing experiences.",
    },
    {
      title: "How do I get started?",
      content:
        "Getting started is simple! Sign up for an account, choose your plan, and you'll have access to our full suite of tools. Our onboarding process will guide you through the basics.",
    },
    {
      title: "What kind of support do you offer?",
      content:
        "We provide comprehensive support through multiple channels. Free users get access to our knowledge base and community forums. Premium users enjoy priority email support with 24-hour response times.",
    },
    {
      title: "Can I upgrade or downgrade my plan?",
      content:
        "Absolutely! You can change your plan at any time from your account settings. Upgrades take effect immediately, while downgrades will take effect at the end of your current billing cycle.",
    },
    {
      title: "Is my data secure?",
      content:
        "Security is our top priority. We use industry-standard encryption, regular security audits, and comply with major data protection regulations including GDPR and CCPA.",
    },
  ];

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "brainRot",
          colorTemplate: colorTemplate,
          textAnimation: "slide",
        }}
      >
        <ImageFAQ items={faqItems} />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function BrainRotImageFAQPage() {
  return (
    <Suspense>
      <BrainRotImageFAQContent />
    </Suspense>
  );
}
