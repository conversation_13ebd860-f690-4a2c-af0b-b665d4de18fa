"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";
import CentralFAQ from "@/components/sections/layouts/faq/CentralFAQ";

const faqItems = [
  {
    title: "What makes our platform unique?",
    content:
      "Our platform combines cutting-edge technology with user-centric design to deliver an unparalleled experience. We focus on innovation, security, and scalability to ensure our users get the best possible service. Our team of experts continuously works to improve and enhance the platform based on user feedback and industry best practices.",
  },
  {
    title: "How do I get started?",
    content:
      "Getting started is simple! Sign up for an account, choose your plan, and you'll have access to our full suite of tools. Our onboarding process will guide you through the basics, and you can upgrade or customize your experience as you grow. We also provide comprehensive documentation and tutorials to help you make the most of our platform.",
  },
  {
    title: "What pricing plans do you offer?",
    content:
      "We offer flexible pricing plans to suit different needs and budgets. Our basic plan is free and includes core features perfect for individuals and small projects. Premium plans unlock advanced features, increased limits, and priority support. Enterprise customers can access custom solutions with dedicated support and SLA guarantees.",
  },
  {
    title: "Can I upgrade or downgrade my plan?",
    content:
      "Absolutely! You can change your plan at any time from your account settings. Upgrades take effect immediately, giving you instant access to new features. Downgrades will take effect at the end of your current billing cycle, so you won't lose any paid time. We also offer prorated billing for mid-cycle changes on annual plans.",
  },
  {
    title: "What kind of support do you offer?",
    content:
      "We provide comprehensive support through multiple channels. Free users get access to our knowledge base and community forums. Premium users enjoy priority email support with 24-hour response times. Enterprise customers receive dedicated account managers and phone support. We also offer live chat during business hours and regular webinars for training and updates.",
  },
];

function BrainRotCentralFAQContent() {
  const searchParams = useSearchParams();
  const theme = (Number(searchParams.get("theme")) || 1) as ColorTemplate;

  return (
    <SiteThemeProvider
      theme={{
        styleVariant: "brainRot",
        colorTemplate: theme,
        textAnimation: "slide",
      }}
    >
      <CentralFAQ items={faqItems} />
    </SiteThemeProvider>
  );
}

export default function BrainRotCentralFAQPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense fallback={<div>Loading...</div>}>
        <BrainRotCentralFAQContent />
      </Suspense>
    </ReactLenis>
  );
}
