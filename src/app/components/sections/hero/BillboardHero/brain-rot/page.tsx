'use client';

import PlayfulHero from '@/components/sections/layouts/hero/BillboardHero';
import { SiteThemeProvider } from '@/components/sections/ThemeProvider';
import { useSearchParams } from 'next/navigation';
import { ColorTemplate } from '@/components/sections/ThemeProvider';
import { Suspense } from 'react';

function BrainRotHeroContent() {
    const searchParams = useSearchParams();
    const themeParam = searchParams.get('theme');
    const colorTemplate: ColorTemplate = themeParam === '2' ? 2 : 1;

    return (
        <SiteThemeProvider theme={{ 
            styleVariant: 'brainRot', 
            colorTemplate: colorTemplate,
            textAnimation: 'slide'
        }}>
            <PlayfulHero />
        </SiteThemeProvider>
    );
}

export default function BrainRotBillboardHeroPage() {
    return (
        <Suspense fallback={<div />}>
            <BrainRotHeroContent />
        </Suspense>
    );
}