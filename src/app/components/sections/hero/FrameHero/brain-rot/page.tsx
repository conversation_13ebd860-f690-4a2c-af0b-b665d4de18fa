"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import SimpleHero from "@/components/sections/layouts/hero/FrameHero";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";

function BrainRotSimpleHeroContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) ||
    1) as ColorTemplate;

  return (
    <SiteThemeProvider
      theme={{
        styleVariant: "brainRot",
        colorTemplate: colorTemplate,
        textAnimation: "slide",
      }}
    >
      <SimpleHero
        title="The People's Cryptocurrency"
        description="FLOKI is the utility token of the Floki ecosystem."
        primaryButtonText="Learn More"
        secondaryButtonText="Explore"
      />
    </SiteThemeProvider>
  );
}

export default function BrainRotSimpleHeroPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense>
        <BrainRotSimpleHeroContent />
      </Suspense>
    </ReactLenis>
  );
}
