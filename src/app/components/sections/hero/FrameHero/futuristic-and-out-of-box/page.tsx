"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import SimpleHero from "@/components/sections/layouts/hero/FrameHero";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";

function FuturisticSimpleHeroContent() {
  const searchParams = useSearchParams();
  const colorTemplate = (Number(searchParams.get("theme")) ||
    1) as ColorTemplate;

  return (
    <SiteThemeProvider
      theme={{
        styleVariant: "futuristicAndOutOfBox",
        colorTemplate: colorTemplate,
        textAnimation: "slide",
      }}
    >
      <SimpleHero
        title="The People's Cryptocurrency"
        description="FLOKI is the utility token of the Floki ecosystem."
        primaryButtonText="Learn More"
        secondaryButtonText="Explore"
      />
    </SiteThemeProvider>
  );
}

export default function FuturisticAndOutOfBoxPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Suspense>
        <FuturisticSimpleHeroContent />
      </Suspense>
    </ReactLenis>
  );
}
