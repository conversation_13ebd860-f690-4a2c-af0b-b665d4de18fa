"use client";

import React, { Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { ReactLenis } from "lenis/react";
import { PageNav } from "@/components/common/PageNav";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";
import SimpleHero from "@/components/sections/layouts/hero/FrameHero";

function FunAndTrendyHeroContent() {
  const searchParams = useSearchParams();
  const colorTemplate =
    (searchParams.get("theme") as unknown as ColorTemplate) || 1;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "funAndTrendy",
          colorTemplate: Number(colorTemplate) as ColorTemplate,
          textAnimation: "slide",
        }}
      >
        <SimpleHero />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function FunAndTrendyPage() {
  return (
    <Suspense>
      <FunAndTrendyHeroContent />
    </Suspense>
  );
}
