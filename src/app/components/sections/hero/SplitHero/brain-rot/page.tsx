"use client";

import React, { Suspense } from "react";
import { React<PERSON>enis } from "lenis/react";
import CyclopsHero from "@/components/sections/layouts/hero/SplitHero";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";

function BrainRotCyclopsHeroContent() {
  const searchParams = useSearchParams();
  const colorTemplate =
    (searchParams.get("theme") as unknown as ColorTemplate) || 1;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "brainRot",
          colorTemplate: Number(colorTemplate) as ColorTemplate,
          textAnimation: "slide",
        }}
      >
        <CyclopsHero />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function BrainRotCyclopsHeroPage() {
  return (
    <Suspense>
      <BrainRotCyclopsHeroContent />
    </Suspense>
  );
}
