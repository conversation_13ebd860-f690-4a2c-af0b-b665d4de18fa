"use client";

import React, { Suspense } from "react";
import { ReactLenis } from "lenis/react";
import CyclopsHero from "@/components/sections/layouts/hero/TokenBillboardHero";
import { PageNav } from "@/components/common/PageNav";
import { useSearchParams } from "next/navigation";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";

function RetroCyclopsHeroContent() {
  const searchParams = useSearchParams();
  const colorTemplate =
    (searchParams.get("theme") as unknown as ColorTemplate) || 1;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "funAndTrendy",
          colorTemplate: Number(colorTemplate) as ColorTemplate,
          textAnimation: "slide",
        }}
      >
        <CyclopsHero />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function FunAndTrendyCyclopsHeroPage() {
  return (
    <Suspense>
      <RetroCyclopsHeroContent />
    </Suspense>
  );
}
