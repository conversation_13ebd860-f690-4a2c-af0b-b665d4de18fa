"use client";

import React, { Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { ReactLenis } from "lenis/react";
import VoidHero from "@/components/sections/layouts/hero/VoidHero";
import { PageNav } from "@/components/common/PageNav";
import {
  SiteThemeProvider,
  ColorTemplate,
} from "@/components/sections/ThemeProvider";

function BrainRotVoidHeroContent() {
  const searchParams = useSearchParams();
  const colorTemplate =
    (searchParams.get("theme") as unknown as ColorTemplate) || 1;

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <SiteThemeProvider
        theme={{
          styleVariant: "brainRot",
          colorTemplate: Number(colorTemplate) as ColorTemplate,
          textAnimation: "slide",
        }}
      >
        <VoidHero />
      </SiteThemeProvider>
    </ReactLenis>
  );
}

export default function BrainRotVoidHeroPage() {
  return (
    <Suspense>
      <BrainRotVoidHeroContent />
    </Suspense>
  );
}
