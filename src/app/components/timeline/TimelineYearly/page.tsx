'use client';

import React from 'react';
import { ReactLenis } from 'lenis/react';
import TimelineYearly, { type TimelineYearlyItem } from '@/components/timeline/TimelineYearly';
import { PageNav } from '@/components/common/PageNav';

export default function TimelineYearlyPage() {
  const timelineData: TimelineYearlyItem[] = [
    {
      year: "2021",
      title: "Phase 1",
      description: "Initial research and planning phase for the project development including market analysis, stakeholder interviews, competitive assessment, technical feasibility studies, and establishment of core project objectives to ensure successful foundation"
    },
    {
      year: "2022",
      title: "Phase 2",
      description: "Design and prototyping with user feedback integration through iterative design sprints, wireframing, high-fidelity mockups, usability testing, accessibility reviews, and continuous refinement based on user analytics"
    },
    {
      year: "2023",
      title: "Phase 3",
      description: "Development and implementation of core features using agile methodologies, continuous integration, code reviews, performance optimization, API development, cloud infrastructure setup, and progressive feature rollouts"
    },
    {
      year: "2024",
      title: "Phase 4",
      description: "Testing, optimization and final deployment encompassing quality assurance protocols, automated testing, performance benchmarking, user acceptance testing, production deployment, post-launch monitoring, and continuous improvements"
    }
  ];

  return (
    <ReactLenis root>
      <div className="min-h-screen">
        <PageNav />
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">Scroll down to see the effect</p>
        </div>
        <TimelineYearly items={timelineData} />
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">The end</p>
        </div>
      </div>
    </ReactLenis>
  );
}