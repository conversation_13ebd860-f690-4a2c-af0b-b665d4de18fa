'use client';

import React from 'react';
import { ReactLenis } from 'lenis/react';
import TimelinePhoneView from '@/components/timeline/TimelinePhoneView/TimelinePhoneView';
import type { TimelinePhoneViewItem } from '@/components/timeline/TimelinePhoneView/usePhoneAnimations';
import { PageNav } from '@/components/common/PageNav';

const timelineItems: TimelinePhoneViewItem[] = [
  {
    trigger: 'three-content-item-one',
    title: <>Research & Discovery</>,
    description: 'Initial research and planning phase for the project development including market analysis, user interviews, competitive research, and technical feasibility studies to ensure project success',
    image1: '/images/placeholdersmall1.avif',
    image2: '/images/placeholdersmall2.avif'
  },
  {
    trigger: 'three-content-item-two',
    title: <>Design & Prototyping</>,
    description: 'Design and prototyping with user feedback integration through iterative design sprints, wireframing, high-fidelity mockups, and continuous user testing to validate design decisions',
    image1: '/images/placeholdersmall3.avif',
    image2: '/images/placeholdersmall4.avif'
  }
];

export default function TimelinePhoneViewPage() {
  return (
    <ReactLenis root>
      <div className="min-h-screen">
        <PageNav />
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">Scroll down to see the effect</p>
        </div>
        <TimelinePhoneView items={timelineItems} />
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">The end</p>
        </div>
      </div>
    </ReactLenis>
  );
}