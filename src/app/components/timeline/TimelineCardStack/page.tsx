'use client';

import React from 'react';
import { ReactLenis } from 'lenis/react';
import TimelineCardStack from '@/components/timeline/TimelineCardStack';
import { PageNav } from '@/components/common/PageNav';
import type { TimelineCardStackItem } from '@/types/timeline';

export default function TimelineCardStackPage() {
  const timelineItems: TimelineCardStackItem[] = [
    {
      id: 1,
      title: 'Research & Discovery Phase',
      description: 'Initial research and planning phase for the project development including market analysis, user interviews, competitive research, and technical feasibility studies.',
      image: '/images/placeholder1.avif'
    },
    {
      id: 2,
      title: 'Design & Prototyping',
      description: 'Creating wireframes, mockups, and interactive prototypes. User testing and feedback integration to refine the design approach and ensure optimal user experience.',
      image: '/images/placeholder2.avif'
    },
    {
      id: 3,
      title: 'Development & Launch',
      description: 'Building the product with modern technologies, implementing features, testing, and deploying to production. Continuous monitoring and optimization post-launch.',
      image: '/images/placeholder3.avif'
    }
  ];

  return (
    <ReactLenis root>
      <div className="min-h-screen">
        <PageNav />
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">Scroll down to see the effect</p>
        </div>
        <TimelineCardStack items={timelineItems} />
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">The end</p>
        </div>
      </div>
    </ReactLenis>
  );
}