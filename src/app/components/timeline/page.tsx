"use client";

import React from "react";
import Link from "next/link";
import { PageNav } from "@/components/common/PageNav";

const timelineComponents = [
  { name: "TimelineBase", href: "/components/timeline/TimelineBase" },
  { name: "TimelineCardPopupDetail", href: "/components/timeline/TimelineCardPopupDetail" },
  { name: "TimelineHorizontal", href: "/components/timeline/TimelineHorizontal" },
  { name: "TimelineCardStack", href: "/components/timeline/TimelineCardStack" },
  { name: "Timeline3DCardStack", href: "/components/timeline/Timeline3DCardStack" },
  { name: "TimelineProcessFlow", href: "/components/timeline/TimelineProcessFlow" },
  { name: "TimelineYearly", href: "/components/timeline/TimelineYearly" },
  { name: "TimelinePhoneView", href: "/components/timeline/TimelinePhoneView" },
];
export default function TimelinePage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {timelineComponents.map((component) => (
            <Link key={component.name} href={component.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{component.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
