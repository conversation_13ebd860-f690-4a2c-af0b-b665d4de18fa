'use client';

import React from 'react';
import { ReactLenis } from 'lenis/react';
import TimelineHorizontal from '@/components/timeline/TimelineHorizontal/TimelineHorizontal';
import { PageNav } from '@/components/common/PageNav';

const timelineData = [
  {
    title: 'Research & Discovery Phase',
    description: 'Initial research and planning phase for the project development including market analysis, user interviews, competitive research, and technical feasibility studies to ensure project success',
    image: '/images/placeholderextrawide1.webp',
    mobileImage: '/images/placeholder1.avif'
  },
  {
    title: 'Design & Prototyping Phase',
    description: 'Design and prototyping with user feedback integration through iterative design sprints, wireframing, high-fidelity mockups, and continuous user testing to validate design decisions',
    image: '/images/placeholderextrawide2.webp',
    mobileImage: '/images/placeholder2.avif'
  },
  {
    title: 'Development & Implementation',
    description: 'Development and implementation of core features using agile methodologies, continuous integration, code reviews, and quality assurance to deliver a robust and scalable solution',
    image: '/images/placeholderextrawide3.webp',
    mobileImage: '/images/placeholder3.avif'
  }
];

export default function TimelineHorizontalPage() {
  return (
    <ReactLenis root>
      <div className="min-h-screen">
        <PageNav />
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">Scroll down to see the effect</p>
        </div>
        <TimelineHorizontal
          items={timelineData}
        />
        <div className="h-screen flex items-center justify-center">
          <p className="text-base text-center">The end</p>
        </div>
      </div>
    </ReactLenis>
  );
}