"use client";

import React from "react";
import TextboxStandard from "@/components/textbox/TextboxStandard";
import { PageNav } from "@/components/common/PageNav";
import { ReactLenis } from "lenis/react";
import TextScrollBackgroundHighlight from "@/components/text/TextScrollBackgroundHighlight";
import TextScrollTransformRotate from "@/components/text/TextScrollTransformRotate";
import TextScrollEntranceSlide from "@/components/text/TextScrollEntranceSlide";
import TextScrollRevealBlur from "@/components/text/TextScrollRevealBlur";

const TITLE_TEXT = "Secure by design";
const DESCRIPTION_TEXT =
  "Bank confidently with enhanced FDIC coverage up to hundreds of millions, granular user controls, advanced authentication, and federally regulated bank partners.";

export default function TextboxStandardPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxStandard
          title={<h1>{TITLE_TEXT}</h1>}
          description={<p>{DESCRIPTION_TEXT}</p>}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxStandard
          title={
            <TextScrollBackgroundHighlight
              text={TITLE_TEXT}
              variant="trigger"
            />
          }
          description={
            <TextScrollBackgroundHighlight
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxStandard
          title={
            <TextScrollTransformRotate text={TITLE_TEXT} variant="trigger" />
          }
          description={
            <TextScrollTransformRotate
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxStandard
          title={
            <TextScrollEntranceSlide text={TITLE_TEXT} variant="trigger" />
          }
          description={
            <TextScrollEntranceSlide
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxStandard
          title={<TextScrollRevealBlur text={TITLE_TEXT} variant="trigger" />}
          description={
            <TextScrollRevealBlur
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        />
      </div>

      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">The end</p>
      </div>
    </ReactLenis>
  );
}
