"use client";

import React from "react";
import TextboxTaggable from "@/components/textbox/TextboxTaggable";
import { PageNav } from "@/components/common/PageNav";
import { React<PERSON>enis } from "lenis/react";
import TextScrollBackgroundHighlight from "@/components/text/TextScrollBackgroundHighlight";
import TextScrollTransformRotate from "@/components/text/TextScrollTransformRotate";
import TextScrollEntranceSlide from "@/components/text/TextScrollEntranceSlide";
import TextScrollRevealBlur from "@/components/text/TextScrollRevealBlur";
import ButtonTextStagger from "@/components/buttons/ButtonTextStagger/ButtonTextStagger";
import { Zap } from "lucide-react";

const TITLE_TEXT = "Secure by design";
const DESCRIPTION_TEXT =
  "Bank confidently with enhanced FDIC coverage up to hundreds of millions, granular user controls, advanced authentication, and federally regulated bank partners.";
const TAG_ICON = <Zap className="h-[var(--text-sm)] w-auto" />;
const TAG_LABEL = "Our Services";

export default function TextboxTaggablePage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxTaggable
          icon={TAG_ICON}
          label={TAG_LABEL}
          title={<h1>{TITLE_TEXT}</h1>}
          description={<p>{DESCRIPTION_TEXT}</p>}
        >
          <div className="flex items-center gap-3">
            <ButtonTextStagger text="Start Building Now" className="px-6" />
            <ButtonTextStagger
              text="Explore Features"
              className="px-6 text-white"
              bgClassName="!bg-black"
            />
          </div>
        </TextboxTaggable>
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxTaggable
          icon={TAG_ICON}
          label={TAG_LABEL}
          title={
            <TextScrollBackgroundHighlight
              text={TITLE_TEXT}
              variant="trigger"
            />
          }
          description={
            <TextScrollBackgroundHighlight
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        >
          <div className="flex items-center gap-3">
            <ButtonTextStagger text="Start Building Now" className="px-6" />
            <ButtonTextStagger
              text="Explore Features"
              className="px-6 text-white"
              bgClassName="!bg-black"
            />
          </div>
        </TextboxTaggable>
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxTaggable
          icon={TAG_ICON}
          label={TAG_LABEL}
          title={
            <TextScrollTransformRotate text={TITLE_TEXT} variant="trigger" />
          }
          description={
            <TextScrollTransformRotate
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        >
          <div className="flex items-center gap-3">
            <ButtonTextStagger text="Start Building Now" className="px-6" />
            <ButtonTextStagger
              text="Explore Features"
              className="px-6 text-white"
              bgClassName="!bg-black"
            />
          </div>
        </TextboxTaggable>
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxTaggable
          icon={TAG_ICON}
          label={TAG_LABEL}
          title={
            <TextScrollEntranceSlide text={TITLE_TEXT} variant="trigger" />
          }
          description={
            <TextScrollEntranceSlide
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        >
          <div className="flex items-center gap-3">
            <ButtonTextStagger text="Start Building Now" className="px-6" />
            <ButtonTextStagger
              text="Explore Features"
              className="px-6 text-white"
              bgClassName="!bg-black"
            />
          </div>
        </TextboxTaggable>
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxTaggable
          icon={TAG_ICON}
          label={TAG_LABEL}
          title={<TextScrollRevealBlur text={TITLE_TEXT} variant="trigger" />}
          description={
            <TextScrollRevealBlur
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        >
          <div className="flex items-center gap-3">
            <ButtonTextStagger text="Start Building Now" className="px-6" />
            <ButtonTextStagger
              text="Explore Features"
              className="px-6 text-white"
              bgClassName="!bg-black"
            />
          </div>
        </TextboxTaggable>
      </div>

      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">The end</p>
      </div>
    </ReactLenis>
  );
}
