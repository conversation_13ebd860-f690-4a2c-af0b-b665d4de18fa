"use client";

import React from "react";
import TextboxInline from "@/components/textbox/TextboxInline";
import { PageNav } from "@/components/common/PageNav";
import { ReactLenis } from "lenis/react";
import TextScrollBackgroundHighlight from "@/components/text/TextScrollBackgroundHighlight";
import TextScrollTransformRotate from "@/components/text/TextScrollTransformRotate";
import TextScrollEntranceSlide from "@/components/text/TextScrollEntranceSlide";
import TextScrollRevealBlur from "@/components/text/TextScrollRevealBlur";

const TITLE_TEXT = "Why us";
const DESCRIPTION_TEXT =
  "At Dialedweb, we embody the startup mindset. Dynamic, innovative, and hungry to make a difference. We don't just create amazing works, we partner with our clients to revolutionize their industries through groundbreaking digital experiences. From redefining brand engagement to boosting conversions, every project we take on is an opportunity to challenge the norm, deliver excellence, and leave an impact.";

export default function TextboxInlinePage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxInline
          title={<span>{TITLE_TEXT}</span>}
          description={<span>{DESCRIPTION_TEXT}</span>}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxInline
          title={
            <TextScrollBackgroundHighlight
              text={TITLE_TEXT}
              variant="trigger"
            />
          }
          description={
            <TextScrollBackgroundHighlight
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxInline
          title={
            <TextScrollTransformRotate text={TITLE_TEXT} variant="trigger" />
          }
          description={
            <TextScrollTransformRotate
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxInline
          title={
            <TextScrollEntranceSlide text={TITLE_TEXT} variant="trigger" />
          }
          description={
            <TextScrollEntranceSlide
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxInline
          title={<TextScrollRevealBlur text={TITLE_TEXT} variant="trigger" />}
          description={
            <TextScrollRevealBlur
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        />
      </div>

      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">The end</p>
      </div>
    </ReactLenis>
  );
}
