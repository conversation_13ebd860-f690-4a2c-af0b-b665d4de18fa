"use client";

import React from "react";
import TextboxLayoutHorizontal from "@/components/textbox/TextboxLayoutHorizontal";
import { PageNav } from "@/components/common/PageNav";
import { ReactLenis } from "lenis/react";
import TextScrollBackgroundHighlight from "@/components/text/TextScrollBackgroundHighlight";
import TextScrollTransformRotate from "@/components/text/TextScrollTransformRotate";
import TextScrollEntranceSlide from "@/components/text/TextScrollEntranceSlide";
import TextScrollRevealBlur from "@/components/text/TextScrollRevealBlur";

const TITLE_TEXT = "Empower business growth and innovation";
const DESCRIPTION_TEXT =
  "Bank confidently with enhanced FDIC coverage up to hundreds of millions, granular user controls, advanced authentication, and federally regulated bank partners.";

export default function TextboxLayoutHorizontalPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxLayoutHorizontal
          title={<h1>{TITLE_TEXT}</h1>}
          description={<p>{DESCRIPTION_TEXT}</p>}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxLayoutHorizontal
          title={
            <TextScrollBackgroundHighlight
              text="Visual identity design"
              variant="trigger"
            />
          }
          description={
            <div className="flex flex-col gap-2 md:gap-6">
              <TextScrollBackgroundHighlight
                text="Gain a competitive edge, enhance your brand perception, and achieve brand clarity."
                className="md:!text-xl"
                variant="words-trigger"
              />
              <TextScrollBackgroundHighlight
                text="Your identity drives change, increses the value of your company, and cotributes to your organization to move faster and more efficiently."
                className="md:!text-xl"
                variant="words-trigger"
              />
              <TextScrollBackgroundHighlight
                text="A professional corporate identity attracts the attention of potential customers and makes your buisness a preferred choice. A well-defined and consistent brand conveysprofessionalism, and positively impacts customer loyaltyand influences premium-brand perception."
                className="md:!text-xl"
                variant="words-trigger"
              />
            </div>
          }
          alignStart
          descriptionClassName="md:!w-[40%]"
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxLayoutHorizontal
          title={
            <TextScrollBackgroundHighlight
              text="Spending time looking for your parcel around the city is a thing of the past."
              className="!leading-[105%] !font-medium"
              variant="trigger"
            />
          }
          description={
            <TextScrollBackgroundHighlight
              text="Never let a parcel delivery rule your entire schedule again."
              className="!text-xl"
              variant="words-trigger"
            />
          }
          reverseLayout={true}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxLayoutHorizontal
          title={
            <TextScrollBackgroundHighlight
              text={TITLE_TEXT}
              variant="trigger"
            />
          }
          description={
            <TextScrollBackgroundHighlight
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxLayoutHorizontal
          title={
            <TextScrollTransformRotate text={TITLE_TEXT} variant="trigger" />
          }
          description={
            <TextScrollTransformRotate
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxLayoutHorizontal
          title={
            <TextScrollEntranceSlide text={TITLE_TEXT} variant="trigger" />
          }
          description={
            <TextScrollEntranceSlide
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        />
      </div>

      <div className="min-h-screen flex items-center justify-center px-[var(--width-10)]">
        <TextboxLayoutHorizontal
          title={<TextScrollRevealBlur text={TITLE_TEXT} variant="trigger" />}
          description={
            <TextScrollRevealBlur
              text={DESCRIPTION_TEXT}
              variant="words-trigger"
            />
          }
        />
      </div>

      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">The end</p>
      </div>
    </ReactLenis>
  );
}
