"use client";

import React from "react";
import AccordionLayoutBentoExpand from "@/components/accordions/AccordionLayoutBentoExpand";
import { PageNav } from "@/components/common/PageNav";
import { React<PERSON>enis } from "lenis/react";

const accordionData = [
  {
    title: "Enterprise Architecture",
    content:
      "Building scalable and maintainable application structures with modern design patterns and best practices is at the core of our development philosophy. Our architecture approach focuses on microservices, event-driven systems, and cloud-native solutions that can handle millions of concurrent users while maintaining high availability and fault tolerance. We implement Domain-Driven Design (DDD) principles, CQRS patterns, and event sourcing to create systems that are not only robust but also flexible enough to evolve with changing business requirements.",
  },
  {
    title: "Full-Stack Development",
    content:
      "Creating robust features with clean code, comprehensive testing, and continuous integration workflows defines our development methodology. We leverage modern frameworks like React, Next.js, Vue.js, and Node.js to build performant applications with server-side rendering, API routes, and real-time capabilities. Our development process includes Test-Driven Development (TDD), behavior-driven development (BDD), and extensive code reviews to ensure quality. We utilize TypeScript for type safety, implement GraphQL for efficient data fetching",
  },
  {
    title: "User Experience Design",
    content:
      "Crafting beautiful user interfaces with intuitive interactions and accessible experiences for all users is our design mission. Our design philosophy emphasizes responsive layouts, smooth animations, and thoughtful micro-interactions that delight users while maintaining WCAG 2.1 AA compliance. We conduct user research, create personas, and develop journey maps to understand user needs deeply. Our design process includes wireframing, prototyping with Figma and Adobe XD, usability testing, and A/B testing to validate design decisions.",
  },
  {
    title: "Performance Optimization",
    content:
      "Optimizing applications for speed and efficiency with advanced techniques ensures exceptional user experiences across all devices and network conditions. We implement code splitting, lazy loading, and intelligent caching strategies to minimize initial load times. Our optimization toolkit includes tree shaking, bundle analysis, critical CSS extraction, and image optimization with next-gen formats. We leverage edge computing with CDNs, implement service workers for offline functionality, and utilize HTTP/2 and HTTP/3 protocols.",
  },
  {
    title: "Security & Compliance",
    content:
      "Implementing robust security measures to protect against evolving threats while ensuring regulatory compliance is paramount in modern applications. Our security framework encompasses multiple layers including secure authentication with OAuth 2.0, OpenID Connect, and biometric authentication options. We implement JWT tokens with refresh token rotation, session management, and multi-factor authentication for enhanced security. Our applications feature comprehensive protection against OWASP Top 10 vulnerabilities including SQL injection.",
  },
  {
    title: "Cloud & DevOps",
    content:
      "Managing cloud infrastructure and implementing DevOps best practices enables rapid, reliable, and scalable application deployment. Our expertise spans across major cloud providers including AWS, Google Cloud Platform, and Microsoft Azure. We design and implement Infrastructure as Code using Terraform, CloudFormation, and Pulumi, ensuring reproducible and version-controlled infrastructure. Our DevOps practices include continuous integration and deployment pipelines, automated testing at every stage, and blue-green deployments.",
  },
  {
    title: "Data Engineering",
    content:
      "Building robust data pipelines and analytics platforms that transform raw data into actionable insights drives business intelligence and decision-making. Our data engineering solutions include ETL/ELT pipelines using Apache Airflow, Apache Spark, and cloud-native services like AWS Glue and Azure Data Factory. We design data warehouses and data lakes using technologies like Snowflake, BigQuery, and Databricks, implementing medallion architecture for data quality and governance. Real-time data processing with Apache Kafka.",
  },
  {
    title: "API Development",
    content:
      "Designing and implementing robust APIs that serve as the backbone of modern applications requires careful planning and execution. Our API development expertise includes RESTful services following OpenAPI specifications, GraphQL implementations with Apollo Server, and gRPC for high-performance microservices communication. We implement API gateways using Kong, Apigee, or AWS API Gateway for centralized management, rate limiting, and authentication. Our integration patterns include architectures with message brokers.",
  },
];

export default function AccordionLayoutBentoExpandPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>
      <AccordionLayoutBentoExpand items={accordionData} />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">The end</p>
      </div>
    </ReactLenis>
  );
}
