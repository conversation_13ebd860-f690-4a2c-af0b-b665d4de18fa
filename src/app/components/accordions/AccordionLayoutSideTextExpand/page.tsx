"use client";

import React from "react";
import AccordionLayoutSideTextExpand from "@/components/accordions/AccordionLayoutSideTextExpand";
import { PageNav } from "@/components/common/PageNav";
import { React<PERSON>enis } from "lenis/react";

const accordionData = [
  {
    title: "Enterprise Architecture",
    content:
      "Building scalable and maintainable application structures with modern design patterns and best practices is at the core of our development philosophy. Our architecture approach focuses on microservices, event-driven systems, and cloud-native solutions that can handle millions of concurrent users while maintaining high availability and fault tolerance. We implement Domain-Driven Design (DDD) principles, CQRS patterns, and event sourcing to create systems that are not only robust but also flexible enough to evolve with changing business requirements.",
  },
  {
    title: "Full-Stack Development",
    content:
      "Creating robust features with clean code, comprehensive testing, and continuous integration workflows defines our development methodology. We leverage modern frameworks like React, Next.js, Vue.js, and Node.js to build performant applications with server-side rendering, API routes, and real-time capabilities. Our development process includes Test-Driven Development (TDD), behavior-driven development (BDD), and extensive code reviews to ensure quality. We utilize TypeScript for type safety, implement GraphQL for efficient data fetching",
  },
  {
    title: "User Experience Design",
    content:
      "Crafting beautiful user interfaces with intuitive interactions and accessible experiences for all users is our design mission. Our design philosophy emphasizes responsive layouts, smooth animations, and thoughtful micro-interactions that delight users while maintaining WCAG 2.1 AA compliance. We conduct user research, create personas, and develop journey maps to understand user needs deeply. Our design process includes wireframing, prototyping with Figma and Adobe XD, usability testing, and A/B testing to validate design decisions.",
  },
  {
    title: "Performance Optimization",
    content:
      "Optimizing applications for speed and efficiency with advanced techniques ensures exceptional user experiences across all devices and network conditions. We implement code splitting, lazy loading, and intelligent caching strategies to minimize initial load times. Our optimization toolkit includes tree shaking, bundle analysis, critical CSS extraction, and image optimization with next-gen formats. We leverage edge computing with CDNs, implement service workers for offline functionality, and utilize HTTP/2 and HTTP/3 protocols.",
  },
  {
    title: "Security & Compliance",
    content:
      "Implementing robust security measures to protect against evolving threats while ensuring regulatory compliance is paramount in modern applications. Our security framework encompasses multiple layers including secure authentication with OAuth 2.0, OpenID Connect, and biometric authentication options. We implement JWT tokens with refresh token rotation, session management, and multi-factor authentication for enhanced security. Our applications feature comprehensive protection against OWASP Top 10 vulnerabilities including SQL injection.",
  },
];

export default function AccordionLayoutSideTextExpandPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>
      <AccordionLayoutSideTextExpand
        items={accordionData}
        title="Frequently asked questions"
        description="Find answers to common questions about our services and processes. Can't find what you're looking for? Feel free to get in touch."
      />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">The end</p>
      </div>
    </ReactLenis>
  );
}
