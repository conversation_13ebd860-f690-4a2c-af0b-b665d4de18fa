"use client";

import React from "react";
import { React<PERSON>enis } from "lenis/react";
import TextFillGradient from "@/components/text/TextFillGradient";
import { PageNav } from "@/components/common/PageNav";

export default function TextFillGradientPage() {
  return (
    <ReactLenis root>
      <PageNav />

      <div className="min-h-screen flex items-center justify-center">
        <TextFillGradient
          text="Beautiful Gradient Text"
          className="text-6xl font-semibold text-center max-w-4xl"
        />
      </div>

      <div className="min-h-screen flex items-center justify-center">
        <TextFillGradient
          text="Create stunning visual effects with radial gradients that capture attention and enhance your design."
          className="text-4xl text-center max-w-4xl"
        />
      </div>

      <div className="min-h-screen flex items-center justify-center">
        <TextFillGradient
          text="This gradient text component uses a radial gradient with multiple color stops to create a vibrant, eye-catching effect that draws attention to your important messages."
          className="text-3xl text-center max-w-4xl"
        />
      </div>

      <div className="min-h-screen flex items-center justify-center">
        <TextFillGradient
          text="Perfect for headlines, subheadings, and any text that needs to stand out from the crowd."
          className="text-4xl text-center max-w-4xl"
        />
      </div>
    </ReactLenis>
  );
}
