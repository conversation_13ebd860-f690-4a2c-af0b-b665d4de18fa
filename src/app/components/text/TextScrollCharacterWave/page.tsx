"use client";

import React from "react";
import { React<PERSON>enis } from "lenis/react";
import TextScrollCharacterWave from "@/components/text/TextScrollCharacterWave";
import { PageNav } from "@/components/common/PageNav";

export default function TextScrollCharacterWavePage() {
  return (
    <ReactLenis root>
      <PageNav />

      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>

      <div className="min-h-screen flex items-center justify-center text-center">
        <TextScrollCharacterWave
          text="Experience the wave of innovation"
          className="text-5xl"
        />
      </div>

      <div className="min-h-screen flex items-center justify-center text-center">
        <TextScrollCharacterWave
          text="Transform your digital presence with cutting-edge solutions that drive innovation and deliver exceptional user experiences across all platforms and devices. We leverage the latest technologies and industry best practices to create scalable, performant applications that not only meet but exceed your business objectives, ensuring long-term success in an ever-evolving digital landscape where adaptability and excellence are paramount"
          className="text-xl max-w-5xl"
          stagger={0.025}
        />
      </div>

      <div className="h-screen" />
    </ReactLenis>
  );
}
