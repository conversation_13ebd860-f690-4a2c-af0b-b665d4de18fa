"use client";

import React from "react";
import { React<PERSON>enis } from "lenis/react";
import TextWordRotateCube from "@/components/text/TextWordRotateCube/TextWordRotateCube";
import { PageNav } from "@/components/common/PageNav";

export default function TextWordRotateCubePage() {
  return (
    <ReactLenis root>
      <PageNav />

      <div className="min-h-screen flex items-center justify-center text-center">
        <TextWordRotateCube
          items={["Brands.", "Shows.", "Content.", "Trends."]}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center text-center">
        <TextWordRotateCube
          items={["Design", "Develop", "Deploy", "Deliver"]}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center text-center">
        <TextWordRotateCube
          items={["Create", "Innovate", "Inspire", "Transform"]}
          duration={8}
        />
      </div>

      <div className="min-h-screen flex items-center justify-center text-center">
        <TextWordRotateCube
          items={["Fast", "Secure", "Scalable", "Simple"]}
          duration={15}
        />
      </div>

      <div className="h-screen" />
    </ReactLenis>
  );
}
