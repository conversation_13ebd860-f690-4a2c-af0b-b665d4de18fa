'use client';

import React from 'react';
import Link from 'next/link';
import { PageNav } from '@/components/common/PageNav';

const textComponents = [
  { name: 'TextFillGradient', href: '/components/text/TextFillGradient' },
  { name: 'TextFillGradientHover', href: '/components/text/TextFillGradientHover' },
  { name: 'TextScrollBackgroundHighlight', href: '/components/text/TextScrollBackgroundHighlight' },
  { name: 'TextScrollEntranceSlide', href: '/components/text/TextScrollEntranceSlide' },
  { name: 'TextScrollRevealBlur', href: '/components/text/TextScrollRevealBlur' },
  { name: 'TextScrollTransformScale', href: '/components/text/TextScrollTransformScale' },
  { name: 'TextScrollExpand', href: '/components/text/TextScrollExpand' },
  { name: 'TextScrollFlipThreeD', href: '/components/text/TextScrollFlipThreeD' },
  { name: 'TextParallax', href: '/components/text/TextParallax' },
  { name: 'TextScrollMarqueeLoop', href: '/components/text/TextScrollMarqueeLoop' },
  { name: 'TextMaskReveal', href: '/components/text/TextMaskReveal' },
  { name: 'TextNumberCount', href: '/components/text/TextNumberCount' },
  { name: 'TextWordRotateCube', href: '/components/text/TextWordRotateCube' },
  { name: 'TextScrollCharacterWave', href: '/components/text/TextScrollCharacterWave' },
  { name: 'TextRevealColor', href: '/components/text/TextRevealColor' },
  { name: 'TextRetro', href: '/components/text/TextRetro' }
];

export default function TextPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {textComponents.map((component) => (
            <Link key={component.name} href={component.href}
            >
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{component.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}