"use client";

import React from "react";
import { ReactLenis } from "lenis/react";
import TextParallax from "@/components/text/TextParallax";
import { PageNav } from "@/components/common/PageNav";

export default function TextParallaxPage() {
  return (
    <ReactLenis root>
      <PageNav />

      <div className="min-h-screen flex items-center justify-center">
        <TextParallax
          text="Infinite Scroll"
          baseVelocity={-2}
          textClassName="text-9xl font-semibold font-playfair"
        />
      </div>

      <div className="h-screen" />
    </ReactLenis>
  );
}
