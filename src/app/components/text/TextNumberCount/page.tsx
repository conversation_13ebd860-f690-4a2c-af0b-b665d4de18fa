"use client";

import React, { useState, useEffect } from "react";
import { ReactLenis } from "lenis/react";
import TextNumberCount from "@/components/text/TextNumberCount";
import { PageNav } from "@/components/common/PageNav";

export default function TextNumberCountPage() {
  const [value2, setValue2] = useState(1000);
  const [value4, setValue4] = useState(99.99);

  useEffect(() => {
    const interval = setInterval(() => {
      setValue2((prev) => (prev === 1000 ? 5000 : 1000));
      setValue4((prev) => (prev === 99.99 ? 249.99 : 99.99));
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  return (
    <ReactLenis root>
      <PageNav />

      <div className="min-h-screen flex items-center justify-center text-center">
        <TextNumberCount
          value={value4}
          className="text-8xl font-bold"
          format={{ minimumFractionDigits: 2, maximumFractionDigits: 2 }}
          prefix="$"
        />
      </div>

      <div className="min-h-screen flex items-center justify-center text-center">
        <TextNumberCount
          value={value2}
          className="text-5xl font-bold"
          format={{ useGrouping: true }}
        />
      </div>

      <div className="h-screen" />
    </ReactLenis>
  );
}
