"use client";

import React from "react";
import { React<PERSON>enis } from "lenis/react";
import TextScrollRevealBlur from "@/components/text/TextScrollRevealBlur";
import { PageNav } from "@/components/common/PageNav";

export default function TextScrollRevealBlurPage() {
  return (
    <ReactLenis root>
      <PageNav />
      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>

      <div className="min-h-screen flex items-center justify-center">
        <TextScrollRevealBlur
          text="This animation is tied to scroll position. Characters reveal smoothly as you scroll up and down. The animation scrubs with your scroll movement."
          className="text-4xl text-center max-w-4xl"
          variant="scrub"
        />
      </div>

      <div className="min-h-screen flex items-center justify-center">
        <TextScrollRevealBlur
          text="This animation triggers once when scrolled into view. Characters reveal with a stagger effect, but the animation plays independently of scroll position."
          className="text-4xl text-center max-w-4xl"
          variant="trigger"
        />
      </div>

      <div className="min-h-screen flex items-center justify-center">
        <TextScrollRevealBlur
          text="This animation reveals entire words at once, scrubbing with scroll. Each word fades in as a complete unit, tied to your scroll position for smooth control."
          className="text-4xl text-center max-w-4xl"
          variant="words-scrub"
        />
      </div>

      <div className="min-h-screen flex items-center justify-center">
        <TextScrollRevealBlur
          text="This animation triggers words sequentially when scrolled into view. Each word animates independently but plays once, creating a cascading reveal effect."
          className="text-4xl text-center max-w-4xl"
          variant="words-trigger"
        />
      </div>

      <div className="h-screen" />
    </ReactLenis>
  );
}
