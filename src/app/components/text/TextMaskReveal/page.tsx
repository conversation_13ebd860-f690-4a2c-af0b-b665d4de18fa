'use client';

import React from 'react';
import { ReactLenis } from 'lenis/react';
import MaskText from '@/components/text/MaskText';
import { PageNav } from '@/components/common/PageNav';

export default function MaskTextPage() {
  return (
    <ReactLenis root>
      <PageNav />
      
      <div className="min-h-screen flex items-center justify-center text-center">
        <MaskText
          text="Image Background"
          className="text-7xl font-bold"
          backgroundSrc="https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?w=800"
        />
      </div>

      <div className="min-h-screen flex items-center justify-center text-center">
        <MaskText
          text="Animated Background"
          className="text-8xl font-bold"
          backgroundSrc="https://media.giphy.com/media/xULW8Fw2ZhznUptrYA/giphy.gif"
        />
      </div>

      <div className="h-screen" />
    </ReactLenis>
  );
}