"use client";

import React from "react";
import { React<PERSON>enis } from "lenis/react";
import TextMaskReveal from "@/components/text/TextMaskReveal";
import { PageNav } from "@/components/common/PageNav";

export default function TextMaskRevealPage() {
  return (
    <ReactLenis root>
      <PageNav />

      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Scroll down to see the effect</p>
      </div>

      <div className="min-h-screen flex items-center justify-center overflow-hidden">
        <TextMaskReveal text="Sliding from the left" className="text-9xl" />
      </div>

      <div className="h-screen flex items-center justify-center">
        <p className="text-base text-center">Double</p>
      </div>

      <div className="min-h-screen flex flex-col items-center justify-center overflow-hidden">
        <TextMaskReveal
          text="Sliding from the left"
          className="text-9xl"
          direction="left"
        />
        <TextMaskReveal
          text="Sliding from the left"
          className="text-9xl"
          direction="right"
        />
      </div>

      <div className="h-screen" />
    </ReactLenis>
  );
}
