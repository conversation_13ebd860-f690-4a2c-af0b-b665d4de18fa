'use client';

import React from 'react';
import Link from 'next/link';
import { PageNav } from '@/components/common/PageNav';

const navigations: Array<{ name: string; href: string }> = [
  {
    name: 'NavbarBase',
    href: '/components/navigation/NavbarBase'
  },
  {
    name: 'NavbarStyleApple',
    href: '/components/navigation/NavbarStyleApple'
  },
  {
    name: 'NavbarLayoutFloatingInline',
    href: '/components/navigation/NavbarLayoutFloatingInline'
  },
  {
    name: 'NavbarLayoutFloatingOverlay',
    href: '/components/navigation/NavbarLayoutFloatingOverlay'
  },
  {
    name: 'NavbarStyleMinimal',
    href: '/components/navigation/NavbarStyleMinimal'
  },
  {
    name: 'NavbarLayoutSplitBottom',
    href: '/components/navigation/NavbarLayoutSplitBottom'
  }
];

export default function NavigationPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {navigations.map((navigation) => (
            <Link key={navigation.name} href={navigation.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{navigation.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}