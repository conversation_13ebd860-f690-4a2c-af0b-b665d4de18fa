"use client";

import NavbarStyleApple from "@/components/navigation/NavbarStyleApple/NavbarStyleApple";
import { PageNav } from "@/components/common/PageNav";
import { ReactLenis } from "lenis/react";
import { NavItem } from "@/types/navigation";

export default function NavbarStyleApplePage() {
  const navItems: NavItem[] = [
    { name: "Home", id: "hero" },
    { name: "About", id: "about" },
    { name: "Solutions", id: "solutions" },
    { name: "Services", id: "services" },
    { name: "Contact", id: "contact" },
  ];

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <NavbarStyleApple navItems={navItems} logoSrc="/images/logo.svg" />
      <section id="hero" className="h-screen flex items-center justify-center">
        <p>Hero</p>
      </section>
      <section id="about" className="h-screen flex items-center justify-center">
        <p>About</p>
      </section>
      <section
        id="solutions"
        className="h-screen flex items-center justify-center"
      >
        <p>Solutions</p>
      </section>
      <section
        id="services"
        className="h-screen flex items-center justify-center"
      >
        <p>Services</p>
      </section>
      <section
        id="contact"
        className="h-screen flex items-center justify-center"
      >
        <p>Contact</p>
      </section>
    </ReactLenis>
  );
}
