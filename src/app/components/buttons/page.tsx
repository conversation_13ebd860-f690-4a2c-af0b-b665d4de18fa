"use client";

import React from "react";
import Link from "next/link";
import { PageNav } from "@/components/common/PageNav";

const buttons = [
  {
    name: "ButtonIconRotate",
    href: "/components/buttons/ButtonIconRotate",
  },
  {
    name: "ButtonTextStagger",
    href: "/components/buttons/ButtonTextStagger"
  },
  {
    name: "ButtonShiftHover",
    href: "/components/buttons/ButtonShiftHover"
  },
  {
    name: "ButtonHoverDirectional",
    href: "/components/buttons/ButtonHoverDirectional",
  },
  {
    name: "ButtonHoverBubble",
    href: "/components/buttons/ButtonHoverBubble"
  },
  {
    name: "ButtonTextUnderline",
    href: "/components/buttons/ButtonTextUnderline"
  },
  {
    name: "ButtonIconArrow",
    href: "/components/buttons/ButtonIconArrow"
  },
  {
    name: "ButtonExpandHover",
    href: "/components/buttons/ButtonExpandHover"
  },
  {
    name: "ButtonHoverMagnetic",
    href: "/components/buttons/ButtonHoverMagnetic"
  },
  {
    name: "ButtonSlideBackground",
    href: "/components/buttons/ButtonSlideBackground"
  },
  {
    name: "ButtonBorderAnimated",
    href: "/components/buttons/ButtonBorderAnimated",
  },
  {
    name: "ButtonSelectorState",
    href: "/components/buttons/ButtonSelectorState"
  },
  {
    name: "ButtonPressDepth",
    href: "/components/buttons/ButtonPressDepth"
  },
];

export default function ButtonsPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {buttons.map((button) => (
            <Link key={button.name} href={button.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{button.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
