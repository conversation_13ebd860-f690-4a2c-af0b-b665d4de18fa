"use client";

import React from "react";
import ButtonBorderAnimated from "@/components/buttons/ButtonBorderAnimated/ButtonBorderAnimated";
import { PageNav } from "@/components/common/PageNav";

export default function ButtonBorderAnimatedPage() {
  return (
    <div className="min-h-screen p-page-padding flex items-center justify-center">
      <PageNav />
      <ButtonBorderAnimated
        text="ButtonBorderAnimated"
        onClick={() => console.log("Moving border button clicked!")}
      />
    </div>
  );
}
