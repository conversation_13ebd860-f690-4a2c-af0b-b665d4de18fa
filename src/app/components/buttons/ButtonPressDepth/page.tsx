"use client";

import React from "react";
import ButtonPressDepth from "@/components/buttons/ButtonPressDepth";
import { PageNav } from "@/components/common/PageNav";

export default function ButtonPressDepthPage() {
  return (
    <section className="relative z-10 min-h-screen flex flex-col justify-center px-[var(--width-10)]">
      <PageNav />
      <div className="w-full flex justify-center">
        <ButtonPressDepth
          text="ButtonPressDepth"
          onClick={() => console.log("Pushed!")}
        />
      </div>
    </section>
  );
}
